/**
 * 水体物理编辑器组件
 * 用于编辑水体物理属性
 */
import React, { useState, useEffect } from 'react';
import { Form, InputNumber, Switch, Slider, Button, Card, Tabs, Row, Col, Space } from 'antd';
import {
  WaterOutlined,
  SettingOutlined,
  ExperimentOutlined,
  ThunderboltOutlined,
  DashboardOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { Vector3Input } from '../../common/Vector3Input';

const { TabPane } = Tabs;

/**
 * 水体物理编辑器属性
 */
interface WaterPhysicsEditorProps {
  /** 实体ID */
  entityId: string;
}

/**
 * 水体物理编辑器组件
 */
const WaterPhysicsEditor: React.FC<WaterPhysicsEditorProps> = ({ entityId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  
  // 状态
  const [activeTab, setActiveTab] = useState<string>('basic');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  
  // 从Redux获取水体数据
  const waterBody = useSelector((state: RootState) => 
    state.physics.waterBodies.find(wb => wb.entityId === entityId)
  );
  
  // 初始化表单
  useEffect(() => {
    if (waterBody) {
      form.setFieldsValue({
        // 基本属性
        enabled: waterBody.enabled,
        density: waterBody.density,
        viscosity: waterBody.viscosity,
        resolution: waterBody.resolution,
        size: waterBody.size,
        
        // 物理属性
        enableBuoyancy: waterBody.enableBuoyancy,
        enableDrag: waterBody.enableDrag,
        enableFlow: waterBody.enableFlow,
        enableWaves: waterBody.enableWaves,
        enableCollision: waterBody.enableCollision,
        
        // 高级属性
        enableSpatialPartitioning: waterBody.enableSpatialPartitioning,
        spatialGridSize: waterBody.spatialGridSize,
        enableAdaptiveUpdate: waterBody.enableAdaptiveUpdate,
        minUpdateFrequency: waterBody.minUpdateFrequency,
        maxUpdateFrequency: waterBody.maxUpdateFrequency,
        enableMultithreading: waterBody.enableMultithreading,
        
        // 交互属性
        enableFlowImpact: waterBody.enableFlowImpact,
        enableWaterSplitting: waterBody.enableWaterSplitting,
        flowSpeed: waterBody.flowSpeed,
        flowDirection: waterBody.flowDirection,
        waveHeight: waterBody.waveHeight,
        waveSpeed: waterBody.waveSpeed
      });
    }
  }, [waterBody, form]);
  
  // 处理表单变更
  const handleValuesChange = (_changedValues: any, _allValues: any) => {
    // 更新Redux状态
    if (entityId) {
      // 这里应该dispatch一个action来更新水体属性
      // dispatch(updateWaterBodyProperties(entityId, changedValues));
    }
  };
  
  // 处理保存
  const handleSave = () => {
    form.validateFields().then(_values => {
      // 保存到Redux
      // dispatch(saveWaterBodyProperties(entityId, values));
      setIsEditing(false);
    });
  };
  
  // 处理取消
  const handleCancel = () => {
    // 重置表单
    if (waterBody) {
      form.setFieldsValue({
        // 重置为原始值
        // ...
      });
    }
    setIsEditing(false);
  };
  
  // 渲染基本属性标签页
  const renderBasicTab = () => {
    return (
      <div className="basic-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enabled"
                label={t('editor.physics.water.enabled')}
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="resolution"
                label={t('editor.physics.water.resolution')}
                tooltip={t('editor.physics.water.resolutionTooltip')}
                rules={[{ required: true }]}
              >
                <InputNumber min={8} max={512} step={8} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="size"
            label={t('editor.physics.water.size')}
            tooltip={t('editor.physics.water.sizeTooltip')}
          >
            <Vector3Input />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="density"
                label={t('editor.physics.water.density')}
                tooltip={t('editor.physics.water.densityTooltip')}
                rules={[{ required: true }]}
              >
                <InputNumber min={0.1} max={10} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="viscosity"
                label={t('editor.physics.water.viscosity')}
                tooltip={t('editor.physics.water.viscosityTooltip')}
                rules={[{ required: true }]}
              >
                <InputNumber min={0} max={10} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };
  
  // 渲染物理属性标签页
  const renderPhysicsTab = () => {
    return (
      <div className="physics-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableBuoyancy"
                label={t('editor.physics.water.enableBuoyancy')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableBuoyancyTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableDrag"
                label={t('editor.physics.water.enableDrag')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableDragTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableFlow"
                label={t('editor.physics.water.enableFlow')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableFlowTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableWaves"
                label={t('editor.physics.water.enableWaves')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableWavesTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="enableCollision"
            label={t('editor.physics.water.enableCollision')}
            valuePropName="checked"
            tooltip={t('editor.physics.water.enableCollisionTooltip')}
          >
            <Switch />
          </Form.Item>
        </Form>
      </div>
    );
  };
  
  // 渲染交互属性标签页
  const renderInteractionTab = () => {
    return (
      <div className="interaction-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableFlowImpact"
                label={t('editor.physics.water.enableFlowImpact')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableFlowImpactTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableWaterSplitting"
                label={t('editor.physics.water.enableWaterSplitting')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableWaterSplittingTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="flowSpeed"
            label={t('editor.physics.water.flowSpeed')}
            tooltip={t('editor.physics.water.flowSpeedTooltip')}
          >
            <Slider min={0} max={10} step={0.1} />
          </Form.Item>
          
          <Form.Item
            name="flowDirection"
            label={t('editor.physics.water.flowDirection')}
            tooltip={t('editor.physics.water.flowDirectionTooltip')}
          >
            <Vector3Input />
          </Form.Item>
          
          <Form.Item
            name="waveHeight"
            label={t('editor.physics.water.waveHeight')}
            tooltip={t('editor.physics.water.waveHeightTooltip')}
          >
            <Slider min={0} max={2} step={0.01} />
          </Form.Item>
          
          <Form.Item
            name="waveSpeed"
            label={t('editor.physics.water.waveSpeed')}
            tooltip={t('editor.physics.water.waveSpeedTooltip')}
          >
            <Slider min={0} max={5} step={0.1} />
          </Form.Item>
        </Form>
      </div>
    );
  };
  
  // 渲染高级属性标签页
  const renderAdvancedTab = () => {
    return (
      <div className="advanced-tab">
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleValuesChange}
          disabled={!isEditing}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableSpatialPartitioning"
                label={t('editor.physics.water.enableSpatialPartitioning')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableSpatialPartitioningTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="spatialGridSize"
                label={t('editor.physics.water.spatialGridSize')}
                tooltip={t('editor.physics.water.spatialGridSizeTooltip')}
              >
                <InputNumber min={1} max={100} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enableAdaptiveUpdate"
                label={t('editor.physics.water.enableAdaptiveUpdate')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableAdaptiveUpdateTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enableMultithreading"
                label={t('editor.physics.water.enableMultithreading')}
                valuePropName="checked"
                tooltip={t('editor.physics.water.enableMultithreadingTooltip')}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="minUpdateFrequency"
                label={t('editor.physics.water.minUpdateFrequency')}
                tooltip={t('editor.physics.water.minUpdateFrequencyTooltip')}
              >
                <InputNumber min={1} max={10} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="maxUpdateFrequency"
                label={t('editor.physics.water.maxUpdateFrequency')}
                tooltip={t('editor.physics.water.maxUpdateFrequencyTooltip')}
              >
                <InputNumber min={1} max={60} step={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    );
  };
  
  return (
    <div className="component-editor water-physics-editor">
      <Card 
        title={
          <Space>
            <WaterOutlined />
            {t('editor.physics.water.title')}
          </Space>
        }
        extra={
          <Space>
            {isEditing ? (
              <>
                <Button onClick={handleCancel}>{t('editor.common.cancel')}</Button>
                <Button type="primary" onClick={handleSave}>{t('editor.common.save')}</Button>
              </>
            ) : (
              <Button type="primary" onClick={() => setIsEditing(true)}>{t('editor.common.edit')}</Button>
            )}
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <SettingOutlined />
                {t('editor.physics.water.basicTab')}
              </span>
            } 
            key="basic"
          >
            {renderBasicTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <ExperimentOutlined />
                {t('editor.physics.water.physicsTab')}
              </span>
            } 
            key="physics"
          >
            {renderPhysicsTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <ThunderboltOutlined />
                {t('editor.physics.water.interactionTab')}
              </span>
            } 
            key="interaction"
          >
            {renderInteractionTab()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                <DashboardOutlined />
                {t('editor.physics.water.advancedTab')}
              </span>
            } 
            key="advanced"
          >
            {renderAdvancedTab()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default WaterPhysicsEditor;
